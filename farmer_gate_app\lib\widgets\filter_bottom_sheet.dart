import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/filter_provider.dart';
import '../providers/search_provider.dart';
import '../models/search_filters.dart';

class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({super.key});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late SearchFilters _tempFilters;

  @override
  void initState() {
    super.initState();
    _tempFilters = context.read<FilterProvider>().filters;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Filters',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _tempFilters = SearchFilters();
                    });
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Filter Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCategoryFilter(),
                  const SizedBox(height: 24),
                  _buildSubcategoryFilter(),
                  const SizedBox(height: 24),
                  _buildFreezerSizeFilter(),
                  const SizedBox(height: 24),
                  _buildPriceRangeFilter(),
                  const SizedBox(height: 24),
                  _buildAvailabilityFilter(),
                  const SizedBox(height: 24),
                  _buildDeliveryOptionsFilter(),
                ],
              ),
            ),
          ),
          
          // Bottom Actions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      context.read<FilterProvider>().applyFilters(_tempFilters);
                      context.read<SearchProvider>().applyFilters(_tempFilters);
                      Navigator.pop(context);
                    },
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: FilterConstants.categories.map((category) {
            final isSelected = _tempFilters.category == category;
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _tempFilters = _tempFilters.copyWith(
                    category: selected ? category : null,
                  );
                });
              },
              selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSubcategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Product Type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: FilterConstants.subcategories.map((subcategory) {
            final isSelected = _tempFilters.subcategory == subcategory;
            return FilterChip(
              label: Text(subcategory),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _tempFilters = _tempFilters.copyWith(
                    subcategory: selected ? subcategory : null,
                  );
                });
              },
              selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFreezerSizeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Freezer Size Compatibility',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select your freezer type to see compatible products',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: FilterConstants.freezerSizes.map((size) {
            final isSelected = _tempFilters.freezerSize == size;
            return FilterChip(
              label: Text(size),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _tempFilters = _tempFilters.copyWith(
                    freezerSize: selected ? size : null,
                  );
                });
              },
              selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Price Range',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: FilterConstants.priceRanges.map((range) {
            final isSelected = _tempFilters.priceRange?.min == range.min && 
                              _tempFilters.priceRange?.max == range.max;
            return FilterChip(
              label: Text(range.toString()),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _tempFilters = _tempFilters.copyWith(
                    priceRange: selected ? range : null,
                  );
                });
              },
              selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAvailabilityFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Availability',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: const Text('Hide Out of Stock'),
          subtitle: const Text('Only show products currently available'),
          value: _tempFilters.hideOutOfStock,
          onChanged: (value) {
            setState(() {
              _tempFilters = _tempFilters.copyWith(hideOutOfStock: value);
            });
          },
          activeColor: Theme.of(context).primaryColor,
        ),
      ],
    );
  }

  Widget _buildDeliveryOptionsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Delivery Options',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: const Text('Pickup Only'),
          subtitle: const Text('Show only farms offering pickup'),
          value: _tempFilters.pickupOnly,
          onChanged: (value) {
            setState(() {
              _tempFilters = _tempFilters.copyWith(
                pickupOnly: value,
                deliveryOnly: value ? false : _tempFilters.deliveryOnly,
              );
            });
          },
          activeColor: Theme.of(context).primaryColor,
        ),
        SwitchListTile(
          title: const Text('Delivery Only'),
          subtitle: const Text('Show only farms offering delivery'),
          value: _tempFilters.deliveryOnly,
          onChanged: (value) {
            setState(() {
              _tempFilters = _tempFilters.copyWith(
                deliveryOnly: value,
                pickupOnly: value ? false : _tempFilters.pickupOnly,
              );
            });
          },
          activeColor: Theme.of(context).primaryColor,
        ),
      ],
    );
  }
}
