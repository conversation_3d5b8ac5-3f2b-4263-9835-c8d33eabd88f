import 'package:flutter/material.dart';
import '../models/farm.dart';
import '../models/search_filters.dart';
import '../services/farm_service.dart';

class SearchProvider extends ChangeNotifier {
  final FarmService _farmService = FarmService();
  
  List<Farm> _searchResults = [];
  List<Farm> _allFarms = [];
  SearchFilters _currentFilters = SearchFilters();
  bool _isLoading = false;
  String _searchQuery = '';
  String? _errorMessage;

  // Getters
  List<Farm> get searchResults => _searchResults;
  List<Farm> get allFarms => _allFarms;
  SearchFilters get currentFilters => _currentFilters;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  String? get errorMessage => _errorMessage;

  // Initialize with sample data
  Future<void> initialize() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _allFarms = await _farmService.getAllFarms();
      _searchResults = List.from(_allFarms);
    } catch (e) {
      _errorMessage = 'Failed to load farms: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search by postcode
  Future<void> searchByPostcode(String postcode) async {
    if (postcode.trim().isEmpty) {
      _searchResults = List.from(_allFarms);
      _currentFilters = _currentFilters.copyWith(postcode: null);
      notifyListeners();
      return;
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _searchResults = await _farmService.searchFarmsByPostcode(postcode);
      _currentFilters = _currentFilters.copyWith(postcode: postcode);
    } catch (e) {
      _errorMessage = 'Failed to search farms: ${e.toString()}';
      _searchResults = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Apply filters
  void applyFilters(SearchFilters filters) {
    _currentFilters = filters;
    _filterResults();
    notifyListeners();
  }

  // Update search query
  void updateSearchQuery(String query) {
    _searchQuery = query;
    _filterResults();
    notifyListeners();
  }

  // Clear all filters
  void clearFilters() {
    _currentFilters = SearchFilters();
    _searchQuery = '';
    _searchResults = List.from(_allFarms);
    notifyListeners();
  }

  // Private method to filter results
  void _filterResults() {
    List<Farm> filtered = List.from(_allFarms);

    // Filter by postcode
    if (_currentFilters.postcode != null && _currentFilters.postcode!.isNotEmpty) {
      filtered = filtered.where((farm) => 
        farm.deliveryPostcodes.contains(_currentFilters.postcode)).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((farm) =>
        farm.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        farm.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        farm.location.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    }

    // Filter by category
    if (_currentFilters.category != null && _currentFilters.category!.isNotEmpty) {
      filtered = filtered.where((farm) =>
        farm.categories.contains(_currentFilters.category)).toList();
    }

    // Filter by subcategory
    if (_currentFilters.subcategory != null && _currentFilters.subcategory!.isNotEmpty) {
      filtered = filtered.where((farm) =>
        farm.products.any((product) => 
          product.subcategory == _currentFilters.subcategory)).toList();
    }

    // Filter by freezer size
    if (_currentFilters.freezerSize != null && _currentFilters.freezerSize!.isNotEmpty) {
      filtered = filtered.where((farm) =>
        farm.products.any((product) => 
          product.freezerSize == _currentFilters.freezerSize)).toList();
    }

    // Filter by price range
    if (_currentFilters.priceRange != null) {
      filtered = filtered.where((farm) =>
        farm.products.any((product) => 
          product.price >= _currentFilters.priceRange!.min &&
          product.price <= _currentFilters.priceRange!.max)).toList();
    }

    // Filter out of stock
    if (_currentFilters.hideOutOfStock) {
      filtered = filtered.where((farm) =>
        farm.products.any((product) => product.inStock)).toList();
    }

    // Filter by pickup/delivery preference
    if (_currentFilters.pickupOnly) {
      filtered = filtered.where((farm) => farm.isPickupAvailable).toList();
    }
    if (_currentFilters.deliveryOnly) {
      filtered = filtered.where((farm) => farm.isDeliveryAvailable).toList();
    }

    // Sort results
    _sortResults(filtered);

    _searchResults = filtered;
  }

  // Private method to sort results
  void _sortResults(List<Farm> farms) {
    switch (_currentFilters.sortBy) {
      case 'Price: Low to High':
        farms.sort((a, b) {
          double minPriceA = a.products.isEmpty ? 0 : a.products.map((p) => p.price).reduce((a, b) => a < b ? a : b);
          double minPriceB = b.products.isEmpty ? 0 : b.products.map((p) => p.price).reduce((a, b) => a < b ? a : b);
          return minPriceA.compareTo(minPriceB);
        });
        break;
      case 'Price: High to Low':
        farms.sort((a, b) {
          double maxPriceA = a.products.isEmpty ? 0 : a.products.map((p) => p.price).reduce((a, b) => a > b ? a : b);
          double maxPriceB = b.products.isEmpty ? 0 : b.products.map((p) => p.price).reduce((a, b) => a > b ? a : b);
          return maxPriceB.compareTo(maxPriceA);
        });
        break;
      case 'Rating':
        farms.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'Newest':
        // For now, sort by name as we don't have creation date
        farms.sort((a, b) => a.name.compareTo(b.name));
        break;
      default:
        // Default relevance sorting (by rating then by name)
        farms.sort((a, b) {
          int ratingCompare = b.rating.compareTo(a.rating);
          return ratingCompare != 0 ? ratingCompare : a.name.compareTo(b.name);
        });
    }
  }

  // Get farms count by category
  Map<String, int> getFarmCountByCategory() {
    Map<String, int> counts = {};
    for (String category in FilterConstants.categories) {
      counts[category] = _allFarms.where((farm) => 
        farm.categories.contains(category)).length;
    }
    return counts;
  }
}
