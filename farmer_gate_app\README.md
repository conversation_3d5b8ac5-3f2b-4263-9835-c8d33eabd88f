# Farmer Gate App

A Flutter application for farmer-to-consumer direct ordering, inspired by <PERSON> to <PERSON>idge. This app connects customers directly with local farmers for premium quality meat and produce.

## Features Implemented

### 1. Product Discovery & Search
- **Postcode-based farm finder**: Enter location to find local farms that deliver
- **Advanced search functionality**: Search farms, products, and descriptions
- **Interactive search interface**: Clean, user-friendly search experience
- **Real-time search results**: Instant filtering as you type

### 2. Advanced Filtering System
- **Category filters**: Beef, Chicken, Deer, Eggs & Milk, Goat, Lamb, Pork, Wine & Spirits
- **Subcategory filters**: Bulk Share, By The Cut, Christmas Hams
- **Freezer size compatibility**: Chest, Side By Side, Top/Bottom, Tiny
- **Price range filters**: 0-99, 100-250, 251-500, 500+
- **Stock availability**: Hide out-of-stock option
- **Delivery options**: Pickup only, Delivery only filters
- **Filter chips**: Visual representation of active filters
- **Quick filters**: One-tap category selection

### 3. Farm Location Services
- **Postcode validation**: Australian postcode validation
- **State detection**: Automatic state identification from postcode
- **Delivery area matching**: Check if farms deliver to customer location
- **Distance calculation**: Calculate distances between locations
- **Location permissions**: Handle GPS location access
- **Geocoding services**: Convert between addresses and coordinates

### 4. Search Results Display
- **Farm cards**: Beautiful card-based farm listings
- **Featured farms**: Highlighted top-rated farms
- **Farm details**: Comprehensive farm information pages
- **Product listings**: Detailed product information
- **Rating system**: 5-star rating display
- **Image galleries**: High-quality farm and product images
- **Sorting options**: Sort by relevance, price, rating, distance

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/
│   ├── farm.dart            # Farm and Product data models
│   └── search_filters.dart  # Search filter models
├── providers/
│   ├── search_provider.dart # Search state management
│   ├── farm_provider.dart   # Farm data management
│   └── filter_provider.dart # Filter state management
├── services/
│   ├── farm_service.dart    # Farm data service (with mock data)
│   └── location_service.dart # Location and postcode services
├── screens/
│   ├── home_screen.dart     # Main home screen
│   ├── search_screen.dart   # Search interface
│   └── farm_detail_screen.dart # Farm details page
└── widgets/
    ├── search_bar_widget.dart    # Search input components
    ├── filter_chips_widget.dart  # Filter display components
    ├── filter_bottom_sheet.dart  # Advanced filter modal
    ├── farm_grid_widget.dart     # Farm listing components
    └── featured_farms_widget.dart # Featured farm displays
```

## Key Technologies

- **Flutter**: Cross-platform mobile development
- **Provider**: State management
- **HTTP**: API communication (mock data for demo)
- **Geolocator**: Location services
- **Geocoding**: Address/coordinate conversion
- **Cached Network Image**: Efficient image loading
- **Flutter Rating Bar**: Star rating displays
- **Flutter Map**: Map integration capabilities

## Getting Started

### Prerequisites
- Flutter SDK (>=3.10.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code with Flutter extension
- Android/iOS device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd farmer_gate_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

## Features Overview

### Home Screen
- Hero section with search bar
- Quick stats display
- Featured farms carousel
- Category grid for quick navigation
- Popular products section

### Search Screen
- Postcode-based farm search
- General search functionality
- Advanced filter panel
- Active filter chips
- Sortable results
- Farm grid/list display

### Farm Detail Screen
- Full-screen farm images
- Comprehensive farm information
- Farming practices display
- Product listings
- Contact/order functionality

### Filter System
- Modal bottom sheet for advanced filters
- Category and subcategory selection
- Freezer size compatibility
- Price range selection
- Availability toggles
- Delivery option filters

## Mock Data

The app includes comprehensive mock data featuring:
- 5 sample farms across different Australian states
- Various product categories (Beef, Chicken, Lamb, Pork, Eggs & Milk)
- Different product types (Bulk shares, individual cuts, specialty items)
- Realistic pricing and descriptions
- Farm ratings and reviews
- Delivery postcode coverage

## Future Enhancements

Based on the Farmer to Fridge analysis, potential future features include:

1. **User Authentication**: Customer accounts and profiles
2. **Shopping Cart**: Add products and checkout
3. **Payment Integration**: Secure payment processing
4. **Order Management**: Track orders and delivery
5. **Reviews System**: Customer reviews and ratings
6. **Subscription Boxes**: Recurring deliveries
7. **Farm Registration**: Farmer onboarding system
8. **Real-time Chat**: Customer-farmer communication
9. **Map Integration**: Visual farm locations
10. **Push Notifications**: Order updates and promotions

## Architecture Notes

- **MVVM Pattern**: Using Provider for state management
- **Service Layer**: Separated business logic from UI
- **Mock Data**: Realistic sample data for demonstration
- **Responsive Design**: Optimized for various screen sizes
- **Error Handling**: Comprehensive error states
- **Loading States**: User feedback during operations

## Contributing

This is a demonstration project based on the Farmer to Fridge platform analysis. For production use, consider:

1. Implementing real API endpoints
2. Adding comprehensive error handling
3. Implementing user authentication
4. Adding payment processing
5. Integrating with mapping services
6. Adding push notifications
7. Implementing offline capabilities

## License

This project is for educational and demonstration purposes.
