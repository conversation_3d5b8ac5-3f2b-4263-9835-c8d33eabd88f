import '../models/farm.dart';

class FarmService {
  // In a real app, this would be your API base URL
  static const String baseUrl = 'https://api.farmergate.com';
  
  // For demo purposes, we'll use mock data
  Future<List<Farm>> getAllFarms() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Return mock data
    return _getMockFarms();
  }

  Future<List<Farm>> searchFarmsByPostcode(String postcode) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));
    
    // Filter mock farms by postcode
    List<Farm> allFarms = _getMockFarms();
    return allFarms.where((farm) => 
      farm.deliveryPostcodes.contains(postcode)).toList();
  }

  Future<Farm> getFarmById(String farmId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 400));
    
    List<Farm> allFarms = _getMockFarms();
    Farm? farm = allFarms.where((f) => f.id == farmId).firstOrNull;
    
    if (farm == null) {
      throw Exception('Farm not found');
    }
    
    return farm;
  }

  // Mock data for demonstration
  List<Farm> _getMockFarms() {
    return [
      Farm(
        id: '1',
        name: 'Green Valley Farm',
        description: 'Premium grass-fed beef from our family farm. We\'ve been raising cattle for over 50 years using sustainable farming practices.',
        location: 'Hunter Valley, NSW',
        state: 'NSW',
        suburb: 'Cessnock',
        deliveryPostcodes: ['2325', '2326', '2327', '2000', '2001', '2002'],
        categories: ['Beef'],
        rating: 4.8,
        reviewCount: 127,
        imageUrl: 'https://images.unsplash.com/photo-1500595046743-cd271d694d30?w=400',
        isPickupAvailable: true,
        isDeliveryAvailable: true,
        farmingPractices: {
          'grassFed': true,
          'organic': false,
          'freeRange': true,
          'noAntibiotics': true,
        },
        products: [
          Product(
            id: '1-1',
            name: '5kg Try and See Box',
            description: 'Perfect starter box with premium cuts',
            category: 'Beef',
            subcategory: 'Bulk Share',
            price: 89.99,
            unit: 'box',
            weight: 5.0,
            imageUrl: 'https://images.unsplash.com/photo-1588347818481-c7c1b6b8b6b6?w=300',
            inStock: true,
            freezerSize: 'Tiny',
            details: {'includes': '2kg steak, 1kg roast, 2kg mince'},
          ),
          Product(
            id: '1-2',
            name: '1/8th Beef Share',
            description: '16-18kg of premium Black Angus beef',
            category: 'Beef',
            subcategory: 'Bulk Share',
            price: 299.99,
            unit: 'share',
            weight: 17.0,
            imageUrl: 'https://images.unsplash.com/photo-**********-b9f581a1996d?w=300',
            inStock: true,
            freezerSize: 'Chest',
            details: {'includes': 'Mixed cuts, steaks, roasts, mince'},
          ),
        ],
      ),
      Farm(
        id: '2',
        name: 'Sunrise Poultry Farm',
        description: 'Free-range chickens raised with care in open pastures. Our birds live happy, healthy lives.',
        location: 'Yarra Valley, VIC',
        state: 'VIC',
        suburb: 'Healesville',
        deliveryPostcodes: ['3777', '3778', '3000', '3001', '3002'],
        categories: ['Chicken'],
        rating: 4.6,
        reviewCount: 89,
        imageUrl: 'https://images.unsplash.com/photo-**********-2bdb3c5beed7?w=400',
        isPickupAvailable: false,
        isDeliveryAvailable: true,
        farmingPractices: {
          'grassFed': false,
          'organic': true,
          'freeRange': true,
          'noAntibiotics': true,
        },
        products: [
          Product(
            id: '2-1',
            name: 'Whole Free-Range Chicken',
            description: 'Fresh whole chicken, approximately 1.8kg',
            category: 'Chicken',
            subcategory: 'By The Cut',
            price: 24.99,
            unit: 'each',
            weight: 1.8,
            imageUrl: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=300',
            inStock: true,
            freezerSize: 'Top / Bottom',
            details: {'weight': '1.6-2.0kg average'},
          ),
        ],
      ),
      Farm(
        id: '3',
        name: 'Heritage Pork Co.',
        description: 'Traditional heritage breed pigs raised on pasture. Exceptional flavor and quality.',
        location: 'Adelaide Hills, SA',
        state: 'SA',
        suburb: 'Stirling',
        deliveryPostcodes: ['5152', '5153', '5000', '5001'],
        categories: ['Pork'],
        rating: 4.9,
        reviewCount: 156,
        imageUrl: 'https://images.unsplash.com/photo-1516467508483-a7212febe31a?w=400',
        isPickupAvailable: true,
        isDeliveryAvailable: true,
        farmingPractices: {
          'grassFed': false,
          'organic': false,
          'freeRange': true,
          'noAntibiotics': false,
        },
        products: [
          Product(
            id: '3-1',
            name: 'Christmas Ham',
            description: 'Traditional glazed ham, perfect for celebrations',
            category: 'Pork',
            subcategory: 'Christmas Hams',
            price: 149.99,
            unit: 'each',
            weight: 3.5,
            imageUrl: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=300',
            inStock: true,
            freezerSize: 'Side By Side',
            details: {'weight': '3-4kg average', 'glazed': true},
          ),
        ],
      ),
      Farm(
        id: '4',
        name: 'Mountain View Lamb',
        description: 'Premium lamb from high country pastures. Our sheep graze on native grasses.',
        location: 'Snowy Mountains, NSW',
        state: 'NSW',
        suburb: 'Jindabyne',
        deliveryPostcodes: ['2627', '2628', '2000', '2001'],
        categories: ['Lamb'],
        rating: 4.7,
        reviewCount: 73,
        imageUrl: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400',
        isPickupAvailable: true,
        isDeliveryAvailable: true,
        farmingPractices: {
          'grassFed': true,
          'organic': false,
          'freeRange': true,
          'noAntibiotics': true,
        },
        products: [
          Product(
            id: '4-1',
            name: 'Lamb Leg Roast',
            description: 'Perfect for Sunday roast, 2-2.5kg',
            category: 'Lamb',
            subcategory: 'By The Cut',
            price: 65.99,
            unit: 'each',
            weight: 2.2,
            imageUrl: 'https://images.unsplash.com/photo-1574781330855-d0db8cc6a79c?w=300',
            inStock: true,
            freezerSize: 'Top / Bottom',
            details: {'weight': '2-2.5kg average'},
          ),
        ],
      ),
      Farm(
        id: '5',
        name: 'Coastal Dairy Farm',
        description: 'Fresh milk and eggs from our coastal farm. Our cows graze on salt-enriched pastures.',
        location: 'Great Ocean Road, VIC',
        state: 'VIC',
        suburb: 'Apollo Bay',
        deliveryPostcodes: ['3233', '3234', '3000', '3001'],
        categories: ['Eggs and Milk'],
        rating: 4.5,
        reviewCount: 92,
        imageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400',
        isPickupAvailable: true,
        isDeliveryAvailable: true,
        farmingPractices: {
          'grassFed': true,
          'organic': true,
          'freeRange': true,
          'noAntibiotics': true,
        },
        products: [
          Product(
            id: '5-1',
            name: 'Fresh Farm Eggs',
            description: 'Free-range eggs from happy hens',
            category: 'Eggs and Milk',
            subcategory: 'By The Cut',
            price: 8.99,
            unit: 'dozen',
            weight: 0.7,
            imageUrl: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=300',
            inStock: true,
            freezerSize: 'Tiny',
            details: {'count': '12 eggs', 'size': 'large'},
          ),
        ],
      ),
    ];
  }
}
