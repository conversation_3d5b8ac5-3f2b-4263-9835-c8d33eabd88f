class Farm {
  final String id;
  final String name;
  final String description;
  final String location;
  final String state;
  final String suburb;
  final List<String> deliveryPostcodes;
  final List<String> categories;
  final double rating;
  final int reviewCount;
  final String imageUrl;
  final bool isPickupAvailable;
  final bool isDeliveryAvailable;
  final Map<String, dynamic> farmingPractices;
  final List<Product> products;

  Farm({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.state,
    required this.suburb,
    required this.deliveryPostcodes,
    required this.categories,
    required this.rating,
    required this.reviewCount,
    required this.imageUrl,
    required this.isPickupAvailable,
    required this.isDeliveryAvailable,
    required this.farmingPractices,
    required this.products,
  });

  factory Farm.fromJson(Map<String, dynamic> json) {
    return Farm(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      state: json['state'] ?? '',
      suburb: json['suburb'] ?? '',
      deliveryPostcodes: List<String>.from(json['deliveryPostcodes'] ?? []),
      categories: List<String>.from(json['categories'] ?? []),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      imageUrl: json['imageUrl'] ?? '',
      isPickupAvailable: json['isPickupAvailable'] ?? false,
      isDeliveryAvailable: json['isDeliveryAvailable'] ?? true,
      farmingPractices: json['farmingPractices'] ?? {},
      products: (json['products'] as List<dynamic>?)
              ?.map((p) => Product.fromJson(p))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'state': state,
      'suburb': suburb,
      'deliveryPostcodes': deliveryPostcodes,
      'categories': categories,
      'rating': rating,
      'reviewCount': reviewCount,
      'imageUrl': imageUrl,
      'isPickupAvailable': isPickupAvailable,
      'isDeliveryAvailable': isDeliveryAvailable,
      'farmingPractices': farmingPractices,
      'products': products.map((p) => p.toJson()).toList(),
    };
  }
}

class Product {
  final String id;
  final String name;
  final String description;
  final String category;
  final String subcategory;
  final double price;
  final String unit;
  final double weight;
  final String imageUrl;
  final bool inStock;
  final String freezerSize;
  final Map<String, dynamic> details;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.subcategory,
    required this.price,
    required this.unit,
    required this.weight,
    required this.imageUrl,
    required this.inStock,
    required this.freezerSize,
    required this.details,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      subcategory: json['subcategory'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      unit: json['unit'] ?? 'kg',
      weight: (json['weight'] ?? 0.0).toDouble(),
      imageUrl: json['imageUrl'] ?? '',
      inStock: json['inStock'] ?? true,
      freezerSize: json['freezerSize'] ?? '',
      details: json['details'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'subcategory': subcategory,
      'price': price,
      'unit': unit,
      'weight': weight,
      'imageUrl': imageUrl,
      'inStock': inStock,
      'freezerSize': freezerSize,
      'details': details,
    };
  }
}
