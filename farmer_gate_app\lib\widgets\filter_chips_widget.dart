import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/filter_provider.dart';
import '../providers/search_provider.dart';

class FilterChipsWidget extends StatelessWidget {
  const FilterChipsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<FilterProvider>(
      builder: (context, filterProvider, child) {
        final filters = filterProvider.filters;
        List<Widget> chips = [];

        // Add active filter chips
        if (filters.postcode != null && filters.postcode!.isNotEmpty) {
          chips.add(_buildFilterChip(
            context,
            'Postcode: ${filters.postcode}',
            () {
              filterProvider.updatePostcode(null);
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.category != null && filters.category!.isNotEmpty) {
          chips.add(_buildFilterChip(
            context,
            filters.category!,
            () {
              filterProvider.resetCategory();
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.subcategory != null && filters.subcategory!.isNotEmpty) {
          chips.add(_buildFilterChip(
            context,
            filters.subcategory!,
            () {
              filterProvider.resetSubcategory();
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.freezerSize != null && filters.freezerSize!.isNotEmpty) {
          chips.add(_buildFilterChip(
            context,
            'Freezer: ${filters.freezerSize}',
            () {
              filterProvider.resetFreezerSize();
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.priceRange != null) {
          chips.add(_buildFilterChip(
            context,
            '\$${filters.priceRange.toString()}',
            () {
              filterProvider.resetPriceRange();
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.hideOutOfStock) {
          chips.add(_buildFilterChip(
            context,
            'In Stock Only',
            () {
              filterProvider.updateHideOutOfStock(false);
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.pickupOnly) {
          chips.add(_buildFilterChip(
            context,
            'Pickup Only',
            () {
              filterProvider.updatePickupOnly(false);
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        if (filters.deliveryOnly) {
          chips.add(_buildFilterChip(
            context,
            'Delivery Only',
            () {
              filterProvider.updateDeliveryOnly(false);
              context.read<SearchProvider>().applyFilters(filterProvider.filters);
            },
          ));
        }

        // If no active filters, show empty state
        if (chips.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Active Filters',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  if (chips.isNotEmpty)
                    TextButton(
                      onPressed: () {
                        filterProvider.clearAllFilters();
                        context.read<SearchProvider>().clearFilters();
                      },
                      child: const Text('Clear All'),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: chips,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterChip(BuildContext context, String label, VoidCallback onRemove) {
    return Chip(
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onRemove,
      backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
      deleteIconColor: Theme.of(context).primaryColor,
      labelStyle: TextStyle(
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}

class QuickFilterChips extends StatelessWidget {
  const QuickFilterChips({super.key});

  @override
  Widget build(BuildContext context) {
    final quickFilters = [
      {'label': 'Beef', 'category': 'Beef'},
      {'label': 'Chicken', 'category': 'Chicken'},
      {'label': 'Lamb', 'category': 'Lamb'},
      {'label': 'Pork', 'category': 'Pork'},
      {'label': 'In Stock', 'special': 'inStock'},
      {'label': 'Pickup', 'special': 'pickup'},
    ];

    return Consumer<FilterProvider>(
      builder: (context, filterProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Quick Filters',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: quickFilters.map((filter) {
                  bool isSelected = false;
                  
                  if (filter.containsKey('category')) {
                    isSelected = filterProvider.filters.category == filter['category'];
                  } else if (filter['special'] == 'inStock') {
                    isSelected = filterProvider.filters.hideOutOfStock;
                  } else if (filter['special'] == 'pickup') {
                    isSelected = filterProvider.filters.pickupOnly;
                  }

                  return FilterChip(
                    label: Text(filter['label'] as String),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (filter.containsKey('category')) {
                        filterProvider.updateCategory(
                          selected ? filter['category'] as String : null,
                        );
                      } else if (filter['special'] == 'inStock') {
                        filterProvider.updateHideOutOfStock(selected);
                      } else if (filter['special'] == 'pickup') {
                        filterProvider.updatePickupOnly(selected);
                      }
                      
                      context.read<SearchProvider>().applyFilters(filterProvider.filters);
                    },
                    selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    checkmarkColor: Theme.of(context).primaryColor,
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }
}
