class SearchFilters {
  final String? postcode;
  final String? category;
  final String? subcategory;
  final String? freezerSize;
  final PriceRange? priceRange;
  final bool hideOutOfStock;
  final String? sortBy;
  final bool pickupOnly;
  final bool deliveryOnly;

  SearchFilters({
    this.postcode,
    this.category,
    this.subcategory,
    this.freezerSize,
    this.priceRange,
    this.hideOutOfStock = false,
    this.sortBy,
    this.pickupOnly = false,
    this.deliveryOnly = false,
  });

  SearchFilters copyWith({
    String? postcode,
    String? category,
    String? subcategory,
    String? freezerSize,
    PriceRange? priceRange,
    bool? hideOutOfStock,
    String? sortBy,
    bool? pickupOnly,
    bool? deliveryOnly,
  }) {
    return SearchFilters(
      postcode: postcode ?? this.postcode,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      freezerSize: freezerSize ?? this.freezerSize,
      priceRange: priceRange ?? this.priceRange,
      hideOutOfStock: hideOutOfStock ?? this.hideOutOfStock,
      sortBy: sortBy ?? this.sortBy,
      pickupOnly: pickupOnly ?? this.pickupOnly,
      deliveryOnly: deliveryOnly ?? this.deliveryOnly,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'postcode': postcode,
      'category': category,
      'subcategory': subcategory,
      'freezerSize': freezerSize,
      'priceRange': priceRange?.toJson(),
      'hideOutOfStock': hideOutOfStock,
      'sortBy': sortBy,
      'pickupOnly': pickupOnly,
      'deliveryOnly': deliveryOnly,
    };
  }

  factory SearchFilters.fromJson(Map<String, dynamic> json) {
    return SearchFilters(
      postcode: json['postcode'],
      category: json['category'],
      subcategory: json['subcategory'],
      freezerSize: json['freezerSize'],
      priceRange: json['priceRange'] != null 
          ? PriceRange.fromJson(json['priceRange']) 
          : null,
      hideOutOfStock: json['hideOutOfStock'] ?? false,
      sortBy: json['sortBy'],
      pickupOnly: json['pickupOnly'] ?? false,
      deliveryOnly: json['deliveryOnly'] ?? false,
    );
  }
}

class PriceRange {
  final double min;
  final double max;

  const PriceRange({required this.min, required this.max});

  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
    };
  }

  factory PriceRange.fromJson(Map<String, dynamic> json) {
    return PriceRange(
      min: (json['min'] ?? 0.0).toDouble(),
      max: (json['max'] ?? 999999.0).toDouble(),
    );
  }

  @override
  String toString() {
    if (min == 0 && max == 99) return '0 - 99';
    if (min == 100 && max == 250) return '100 - 250';
    if (min == 251 && max == 500) return '251 - 500';
    if (min == 500) return '500+';
    return '\$${min.toInt()} - \$${max.toInt()}';
  }
}

// Constants for filter options
class FilterConstants {
  static const List<String> categories = [
    'Beef',
    'Chicken', 
    'Deer',
    'Eggs and Milk',
    'Goat',
    'Lamb',
    'Pork',
    'Wine and Spirits',
  ];

  static const List<String> subcategories = [
    'Bulk Share',
    'By The Cut',
    'Christmas Hams',
  ];

  static const List<String> freezerSizes = [
    'Chest',
    'Side By Side',
    'Top / Bottom',
    'Tiny',
  ];

  static const List<PriceRange> priceRanges = [
    PriceRange(min: 0, max: 99),
    PriceRange(min: 100, max: 250),
    PriceRange(min: 251, max: 500),
    PriceRange(min: 500, max: 999999),
  ];

  static const List<String> sortOptions = [
    'Relevance',
    'Price: Low to High',
    'Price: High to Low',
    'Rating',
    'Distance',
    'Newest',
  ];
}
