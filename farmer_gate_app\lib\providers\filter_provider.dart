import 'package:flutter/material.dart';
import '../models/search_filters.dart';

class FilterProvider extends ChangeNotifier {
  SearchFilters _filters = SearchFilters();
  bool _isFilterPanelOpen = false;

  // Getters
  SearchFilters get filters => _filters;
  bool get isFilterPanelOpen => _isFilterPanelOpen;

  // Update individual filter properties
  void updatePostcode(String? postcode) {
    _filters = _filters.copyWith(postcode: postcode);
    notifyListeners();
  }

  void updateCategory(String? category) {
    _filters = _filters.copyWith(category: category);
    notifyListeners();
  }

  void updateSubcategory(String? subcategory) {
    _filters = _filters.copyWith(subcategory: subcategory);
    notifyListeners();
  }

  void updateFreezerSize(String? freezerSize) {
    _filters = _filters.copyWith(freezerSize: freezerSize);
    notifyListeners();
  }

  void updatePriceRange(PriceRange? priceRange) {
    _filters = _filters.copyWith(priceRange: priceRange);
    notifyListeners();
  }

  void updateHideOutOfStock(bool hideOutOfStock) {
    _filters = _filters.copyWith(hideOutOfStock: hideOutOfStock);
    notifyListeners();
  }

  void updateSortBy(String? sortBy) {
    _filters = _filters.copyWith(sortBy: sortBy);
    notifyListeners();
  }

  void updatePickupOnly(bool pickupOnly) {
    _filters = _filters.copyWith(pickupOnly: pickupOnly);
    notifyListeners();
  }

  void updateDeliveryOnly(bool deliveryOnly) {
    _filters = _filters.copyWith(deliveryOnly: deliveryOnly);
    notifyListeners();
  }

  // Apply all filters at once
  void applyFilters(SearchFilters newFilters) {
    _filters = newFilters;
    notifyListeners();
  }

  // Clear all filters
  void clearAllFilters() {
    _filters = SearchFilters();
    notifyListeners();
  }

  // Reset specific filter
  void resetCategory() {
    _filters = _filters.copyWith(category: null);
    notifyListeners();
  }

  void resetSubcategory() {
    _filters = _filters.copyWith(subcategory: null);
    notifyListeners();
  }

  void resetFreezerSize() {
    _filters = _filters.copyWith(freezerSize: null);
    notifyListeners();
  }

  void resetPriceRange() {
    _filters = _filters.copyWith(priceRange: null);
    notifyListeners();
  }

  // Toggle filter panel
  void toggleFilterPanel() {
    _isFilterPanelOpen = !_isFilterPanelOpen;
    notifyListeners();
  }

  void openFilterPanel() {
    _isFilterPanelOpen = true;
    notifyListeners();
  }

  void closeFilterPanel() {
    _isFilterPanelOpen = false;
    notifyListeners();
  }

  // Get active filter count
  int getActiveFilterCount() {
    int count = 0;
    if (_filters.category != null && _filters.category!.isNotEmpty) count++;
    if (_filters.subcategory != null && _filters.subcategory!.isNotEmpty) count++;
    if (_filters.freezerSize != null && _filters.freezerSize!.isNotEmpty) count++;
    if (_filters.priceRange != null) count++;
    if (_filters.hideOutOfStock) count++;
    if (_filters.pickupOnly) count++;
    if (_filters.deliveryOnly) count++;
    return count;
  }

  // Check if any filters are active
  bool get hasActiveFilters => getActiveFilterCount() > 0;

  // Get filter summary text
  String getFilterSummary() {
    List<String> activeFilters = [];
    
    if (_filters.category != null && _filters.category!.isNotEmpty) {
      activeFilters.add(_filters.category!);
    }
    if (_filters.subcategory != null && _filters.subcategory!.isNotEmpty) {
      activeFilters.add(_filters.subcategory!);
    }
    if (_filters.freezerSize != null && _filters.freezerSize!.isNotEmpty) {
      activeFilters.add(_filters.freezerSize!);
    }
    if (_filters.priceRange != null) {
      activeFilters.add(_filters.priceRange.toString());
    }
    if (_filters.hideOutOfStock) {
      activeFilters.add('In Stock Only');
    }
    if (_filters.pickupOnly) {
      activeFilters.add('Pickup Only');
    }
    if (_filters.deliveryOnly) {
      activeFilters.add('Delivery Only');
    }

    if (activeFilters.isEmpty) {
      return 'No filters applied';
    } else if (activeFilters.length == 1) {
      return activeFilters.first;
    } else {
      return '${activeFilters.length} filters applied';
    }
  }
}
