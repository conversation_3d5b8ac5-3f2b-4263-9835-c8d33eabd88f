import 'package:flutter/material.dart';
import '../models/farm.dart';
import '../services/farm_service.dart';

class FarmProvider extends ChangeNotifier {
  final FarmService _farmService = FarmService();
  
  List<Farm> _farms = [];
  List<Farm> _featuredFarms = [];
  Farm? _selectedFarm;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Farm> get farms => _farms;
  List<Farm> get featuredFarms => _featuredFarms;
  Farm? get selectedFarm => _selectedFarm;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Load all farms
  Future<void> loadFarms() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _farms = await _farmService.getAllFarms();
      _featuredFarms = _farms.where((farm) => farm.rating >= 4.5).take(5).toList();
    } catch (e) {
      _errorMessage = 'Failed to load farms: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get farm by ID
  Future<void> getFarmById(String farmId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _selectedFarm = await _farmService.getFarmById(farmId);
    } catch (e) {
      _errorMessage = 'Failed to load farm details: ${e.toString()}';
      _selectedFarm = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get farms by location
  Future<List<Farm>> getFarmsByLocation(String postcode) async {
    try {
      return await _farmService.searchFarmsByPostcode(postcode);
    } catch (e) {
      throw Exception('Failed to get farms by location: ${e.toString()}');
    }
  }

  // Get farms by category
  List<Farm> getFarmsByCategory(String category) {
    return _farms.where((farm) => farm.categories.contains(category)).toList();
  }

  // Get popular farms (high rating and review count)
  List<Farm> getPopularFarms() {
    List<Farm> popular = List.from(_farms);
    popular.sort((a, b) {
      // Sort by rating first, then by review count
      int ratingCompare = b.rating.compareTo(a.rating);
      if (ratingCompare != 0) return ratingCompare;
      return b.reviewCount.compareTo(a.reviewCount);
    });
    return popular.take(10).toList();
  }

  // Get nearby farms (mock implementation - would use actual location in real app)
  List<Farm> getNearbyFarms(String userPostcode) {
    // For now, return farms that deliver to the postcode
    return _farms.where((farm) => 
      farm.deliveryPostcodes.contains(userPostcode)).toList();
  }

  // Clear selected farm
  void clearSelectedFarm() {
    _selectedFarm = null;
    notifyListeners();
  }

  // Refresh farms data
  Future<void> refreshFarms() async {
    await loadFarms();
  }
}
