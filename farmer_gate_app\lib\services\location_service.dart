import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class LocationService {
  // Get current location
  Future<Position?> getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied, we cannot request permissions.');
    }

    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      throw Exception('Failed to get current location: ${e.toString()}');
    }
  }

  // Get postcode from coordinates
  Future<String?> getPostcodeFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        return placemarks.first.postalCode;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get postcode from coordinates: ${e.toString()}');
    }
  }

  // Get coordinates from postcode
  Future<Position?> getCoordinatesFromPostcode(String postcode) async {
    try {
      List<Location> locations = await locationFromAddress('$postcode, Australia');
      
      if (locations.isNotEmpty) {
        final location = locations.first;
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get coordinates from postcode: ${e.toString()}');
    }
  }

  // Calculate distance between two points
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  // Validate Australian postcode
  bool isValidAustralianPostcode(String postcode) {
    // Australian postcodes are 4 digits
    final regex = RegExp(r'^\d{4}$');
    if (!regex.hasMatch(postcode)) return false;

    final code = int.tryParse(postcode);
    if (code == null) return false;

    // Australian postcode ranges
    return (code >= 1000 && code <= 9999);
  }

  // Get state from postcode
  String? getStateFromPostcode(String postcode) {
    final code = int.tryParse(postcode);
    if (code == null) return null;

    // Australian postcode to state mapping
    if (code >= 1000 && code <= 2599) return 'NSW';
    if (code >= 2600 && code <= 2618) return 'ACT';
    if (code >= 2619 && code <= 2899) return 'NSW';
    if (code >= 2900 && code <= 2920) return 'ACT';
    if (code >= 2921 && code <= 2999) return 'NSW';
    if (code >= 3000 && code <= 3999) return 'VIC';
    if (code >= 4000 && code <= 4999) return 'QLD';
    if (code >= 5000 && code <= 5999) return 'SA';
    if (code >= 6000 && code <= 6797) return 'WA';
    if (code >= 6800 && code <= 6999) return 'WA';
    if (code >= 7000 && code <= 7999) return 'TAS';
    if (code >= 8000 && code <= 8999) return 'NT';
    if (code >= 9000 && code <= 9999) return 'WA';

    return null;
  }

  // Check if postcode is in major city
  bool isMajorCity(String postcode) {
    final code = int.tryParse(postcode);
    if (code == null) return false;

    // Major city postcode ranges
    final majorCityRanges = [
      [1000, 2234], // Sydney
      [2600, 2618], // Canberra
      [3000, 3207], // Melbourne
      [4000, 4207], // Brisbane
      [5000, 5199], // Adelaide
      [6000, 6214], // Perth
      [7000, 7199], // Hobart
      [8000, 8099], // Darwin
    ];

    return majorCityRanges.any((range) => code >= range[0] && code <= range[1]);
  }

  // Get delivery zones for a postcode
  List<String> getDeliveryZones(String postcode) {
    final state = getStateFromPostcode(postcode);
    if (state == null) return [];

    // Return nearby postcodes for delivery (simplified logic)
    final code = int.parse(postcode);
    List<String> zones = [];

    // Add the postcode itself
    zones.add(postcode);

    // Add nearby postcodes (within 50km radius approximation)
    for (int i = -20; i <= 20; i++) {
      final nearbyCode = code + i;
      if (nearbyCode >= 1000 && nearbyCode <= 9999) {
        final nearbyPostcode = nearbyCode.toString().padLeft(4, '0');
        if (getStateFromPostcode(nearbyPostcode) == state) {
          zones.add(nearbyPostcode);
        }
      }
    }

    return zones;
  }

  // Mock method to check if delivery is available to postcode
  Future<bool> isDeliveryAvailable(String farmPostcode, String customerPostcode) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 200));

    final farmState = getStateFromPostcode(farmPostcode);
    final customerState = getStateFromPostcode(customerPostcode);

    // Same state delivery is usually available
    if (farmState == customerState) return true;

    // Cross-state delivery for major cities
    if (isMajorCity(farmPostcode) && isMajorCity(customerPostcode)) {
      return true;
    }

    return false;
  }

  // Get estimated delivery time
  Future<String> getEstimatedDeliveryTime(String farmPostcode, String customerPostcode) async {
    await Future.delayed(const Duration(milliseconds: 150));

    final farmState = getStateFromPostcode(farmPostcode);
    final customerState = getStateFromPostcode(customerPostcode);

    if (farmState == customerState) {
      if (isMajorCity(customerPostcode)) {
        return '1-2 business days';
      } else {
        return '2-3 business days';
      }
    } else {
      return '3-5 business days';
    }
  }

  // Get delivery cost estimate
  Future<double> getDeliveryFee(String farmPostcode, String customerPostcode, double orderWeight) async {
    await Future.delayed(const Duration(milliseconds: 100));

    final farmState = getStateFromPostcode(farmPostcode);
    final customerState = getStateFromPostcode(customerPostcode);

    double baseFee = 15.0; // Base delivery fee
    double weightFee = orderWeight * 2.0; // $2 per kg

    // Cross-state delivery surcharge
    if (farmState != customerState) {
      baseFee += 10.0;
    }

    // Remote area surcharge
    if (!isMajorCity(customerPostcode)) {
      baseFee += 5.0;
    }

    return baseFee + weightFee;
  }
}
