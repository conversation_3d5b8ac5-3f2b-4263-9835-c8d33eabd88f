import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/search_provider.dart';
import '../providers/filter_provider.dart';
import '../widgets/filter_bottom_sheet.dart';
import '../widgets/farm_grid_widget.dart';
import '../widgets/filter_chips_widget.dart';

class SearchScreen extends StatefulWidget {
  final String? initialCategory;
  final String? initialPostcode;

  const SearchScreen({
    super.key,
    this.initialCategory,
    this.initialPostcode,
  });

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _postcodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final filterProvider = context.read<FilterProvider>();
      final searchProvider = context.read<SearchProvider>();

      // Apply initial filters if provided
      if (widget.initialCategory != null) {
        filterProvider.updateCategory(widget.initialCategory);
      }
      if (widget.initialPostcode != null) {
        _postcodeController.text = widget.initialPostcode!;
        filterProvider.updatePostcode(widget.initialPostcode);
        searchProvider.searchByPostcode(widget.initialPostcode!);
      }

      // Apply filters to search
      searchProvider.applyFilters(filterProvider.filters);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _postcodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Local Farms'),
        actions: [
          Consumer<FilterProvider>(
            builder: (context, filterProvider, child) {
              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.tune),
                    onPressed: () {
                      _showFilterBottomSheet(context);
                    },
                  ),
                  if (filterProvider.hasActiveFilters)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '${filterProvider.getActiveFilterCount()}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Postcode Input Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Column(
              children: [
                // Postcode Search
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _postcodeController,
                        decoration: const InputDecoration(
                          labelText: 'Enter your postcode',
                          hintText: 'e.g. 2000',
                          prefixIcon: Icon(Icons.location_on),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            context.read<FilterProvider>().updatePostcode(value);
                            context.read<SearchProvider>().searchByPostcode(value);
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        final postcode = _postcodeController.text.trim();
                        if (postcode.isNotEmpty) {
                          context.read<FilterProvider>().updatePostcode(postcode);
                          context.read<SearchProvider>().searchByPostcode(postcode);
                        }
                      },
                      child: const Text('Search'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // General Search
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'Search farms, products...',
                    hintText: 'e.g. grass-fed beef, free-range chicken',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    context.read<SearchProvider>().updateSearchQuery(value);
                  },
                ),
              ],
            ),
          ),

          // Filter Chips
          const FilterChipsWidget(),

          // Results Section
          Expanded(
            child: Consumer<SearchProvider>(
              builder: (context, searchProvider, child) {
                if (searchProvider.isLoading) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Finding farms near you...'),
                      ],
                    ),
                  );
                }

                if (searchProvider.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          searchProvider.errorMessage!,
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            searchProvider.initialize();
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (searchProvider.searchResults.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No farms found',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Try adjusting your search or filters',
                          style: TextStyle(color: Colors.grey[500]),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            _searchController.clear();
                            _postcodeController.clear();
                            context.read<FilterProvider>().clearAllFilters();
                            context.read<SearchProvider>().clearFilters();
                          },
                          child: const Text('Clear All Filters'),
                        ),
                      ],
                    ),
                  );
                }

                return Column(
                  children: [
                    // Results Count
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${searchProvider.searchResults.length} farms found',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Consumer<FilterProvider>(
                            builder: (context, filterProvider, child) {
                              return DropdownButton<String>(
                                value: filterProvider.filters.sortBy ?? 'Relevance',
                                underline: const SizedBox(),
                                items: const [
                                  DropdownMenuItem(value: 'Relevance', child: Text('Relevance')),
                                  DropdownMenuItem(value: 'Price: Low to High', child: Text('Price: Low to High')),
                                  DropdownMenuItem(value: 'Price: High to Low', child: Text('Price: High to Low')),
                                  DropdownMenuItem(value: 'Rating', child: Text('Rating')),
                                  DropdownMenuItem(value: 'Distance', child: Text('Distance')),
                                ],
                                onChanged: (value) {
                                  if (value != null) {
                                    filterProvider.updateSortBy(value);
                                    searchProvider.applyFilters(filterProvider.filters);
                                  }
                                },
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    
                    // Farm Grid
                    Expanded(
                      child: FarmGridWidget(
                        farms: searchProvider.searchResults,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    final filterProvider = context.read<FilterProvider>();
    final searchProvider = context.read<SearchProvider>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const FilterBottomSheet(),
    ).then((_) {
      // Apply filters when bottom sheet is closed
      searchProvider.applyFilters(filterProvider.filters);
    });
  }
}
